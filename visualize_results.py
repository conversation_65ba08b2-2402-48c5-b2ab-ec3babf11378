import SimpleITK as sitk
import numpy as np
import matplotlib.pyplot as plt
import os

def load_and_display_images():
    """
    加载并显示配准和融合结果
    """
    # 文件路径
    fixed_path = "./PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"
    moving_path = "./li_yunqiao_MRI/NIFTI_Output_Folder/brain_only_hd-bet.nii.gz"
    registered_path = "./output/test_MRI/registered_brain_hd-bet.nii.gz"
    fusion_path = "./output/test_MRI/fused_brain_aal.nii.gz"
    
    # 检查文件是否存在
    files_to_check = [fixed_path, moving_path, registered_path, fusion_path]
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在: {file_path}")
            return
    
    # 加载图像
    print("正在加载图像...")
    fixed_image = sitk.ReadImage(fixed_path)
    moving_image = sitk.ReadImage(moving_path)
    registered_image = sitk.ReadImage(registered_path)
    fusion_image = sitk.ReadImage(fusion_path)
    
    # 转换为numpy数组
    fixed_array = sitk.GetArrayFromImage(fixed_image)
    moving_array = sitk.GetArrayFromImage(moving_image)
    registered_array = sitk.GetArrayFromImage(registered_image)
    fusion_array = sitk.GetArrayFromImage(fusion_image)
    
    # 选择中间切片进行显示
    z_slice = fixed_array.shape[0] // 2
    
    # 创建图像显示
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('脑部图像配准和融合结果', fontsize=16, fontweight='bold')
    
    # 第一行：原始图像
    axes[0, 0].imshow(fixed_array[z_slice], cmap='gray')
    axes[0, 0].set_title('固定图像 (AAL模板)')
    axes[0, 0].axis('off')
    
    # 重新采样移动图像到固定图像空间进行显示
    resampler = sitk.ResampleImageFilter()
    resampler.SetReferenceImage(fixed_image)
    resampler.SetInterpolator(sitk.sitkLinear)
    resampler.SetDefaultPixelValue(0)
    resampler.SetTransform(sitk.Transform())
    moving_resampled = resampler.Execute(moving_image)
    moving_resampled_array = sitk.GetArrayFromImage(moving_resampled)
    
    axes[0, 1].imshow(moving_resampled_array[z_slice], cmap='gray')
    axes[0, 1].set_title('移动图像 (原始脑部)')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(registered_array[z_slice], cmap='gray')
    axes[0, 2].set_title('配准后的脑部图像')
    axes[0, 2].axis('off')
    
    # 第二行：对比和融合
    # 配准前后对比
    diff_before = np.abs(fixed_array[z_slice] - moving_resampled_array[z_slice])
    diff_after = np.abs(fixed_array[z_slice] - registered_array[z_slice])
    
    axes[1, 0].imshow(diff_before, cmap='hot')
    axes[1, 0].set_title('配准前差异图')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(diff_after, cmap='hot')
    axes[1, 1].set_title('配准后差异图')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(fusion_array[z_slice], cmap='gray')
    axes[1, 2].set_title('融合图像')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    
    # 保存图像
    output_dir = "./output/test_MRI"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    plt.savefig(os.path.join(output_dir, 'registration_results.png'), dpi=300, bbox_inches='tight')
    print(f"可视化结果已保存至: {os.path.join(output_dir, 'registration_results.png')}")
    
    # 显示图像信息
    print("\n图像信息:")
    print(f"固定图像 (AAL): {fixed_image.GetSize()}, 间距: {fixed_image.GetSpacing()}")
    print(f"移动图像 (脑部): {moving_image.GetSize()}, 间距: {moving_image.GetSpacing()}")
    print(f"配准后图像: {registered_image.GetSize()}, 间距: {registered_image.GetSpacing()}")
    print(f"融合图像: {fusion_image.GetSize()}, 间距: {fusion_image.GetSpacing()}")
    
    plt.show()

def create_3d_slices_view():
    """
    创建三个方向的切片视图
    """
    fusion_path = "./output/test_MRI/fused_brain_aal.nii.gz"
    
    if not os.path.exists(fusion_path):
        print(f"错误: 融合图像不存在: {fusion_path}")
        return
    
    # 加载融合图像
    fusion_image = sitk.ReadImage(fusion_path)
    fusion_array = sitk.GetArrayFromImage(fusion_image)
    
    # 获取中间切片索引
    z_mid = fusion_array.shape[0] // 2  # 轴向
    y_mid = fusion_array.shape[1] // 2  # 冠状
    x_mid = fusion_array.shape[2] // 2  # 矢状
    
    # 创建三方向切片图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle('融合图像 - 三方向切片视图', fontsize=16, fontweight='bold')
    
    # 轴向切片 (Axial)
    axes[0].imshow(fusion_array[z_mid], cmap='gray')
    axes[0].set_title(f'轴向切片 (Z={z_mid})')
    axes[0].axis('off')
    
    # 冠状切片 (Coronal)
    axes[1].imshow(fusion_array[:, y_mid, :], cmap='gray')
    axes[1].set_title(f'冠状切片 (Y={y_mid})')
    axes[1].axis('off')
    
    # 矢状切片 (Sagittal)
    axes[2].imshow(fusion_array[:, :, x_mid], cmap='gray')
    axes[2].set_title(f'矢状切片 (X={x_mid})')
    axes[2].axis('off')
    
    plt.tight_layout()
    
    # 保存图像
    output_dir = "./output/test_MRI"
    plt.savefig(os.path.join(output_dir, 'fusion_3d_slices.png'), dpi=300, bbox_inches='tight')
    print(f"三方向切片图已保存至: {os.path.join(output_dir, 'fusion_3d_slices.png')}")
    
    plt.show()

if __name__ == "__main__":
    print("=== 脑部图像配准和融合结果可视化 ===")
    
    try:
        # 显示配准结果对比
        load_and_display_images()
        
        # 显示融合图像的三方向切片
        create_3d_slices_view()
        
        print("\n✅ 可视化完成！")
        
    except Exception as e:
        print(f"可视化过程中出错: {e}")
        print("请确保已安装 matplotlib: pip install matplotlib")
