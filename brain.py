import subprocess
import os
import sys

def run_brain_extraction_hd_bet(input_image_path, output_image_path):
    """
    使用HD-BET对输入的NIfTI图像进行脑提取。

    参数:
    input_image_path (str): 完整的头部NIfTI图像文件路径。
    output_image_path (str): 提取出的大脑图像的保存路径。

    返回:
    bool: 如果成功则返回True，否则返回False。
    """
    print("--- 开始使用HD-BET进行脑提取 ---")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_image_path):
        print(f"错误: 输入文件不存在: {input_image_path}")
        return False
        
    # 构建要在终端中执行的命令
    # 格式: ["命令", "参数1", "值1", "参数2", "值2", ...]
    command = [
        "hd-bet",
        "-i", input_image_path,
        "-o", output_image_path
    ]
    
    print(f"正在执行命令: {' '.join(command)}")
    
    try:
        # 使用subprocess.run执行命令
        # check=True: 如果命令返回非零退出码（表示错误），则会引发一个CalledProcessError异常。
        # capture_output=True: 捕获标准输出和标准错误。
        # text=True: 将捕获的输出解码为文本。
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        
        # 如果命令成功，打印一些输出信息
        print("HD-BET成功完成！")
        print("--- HD-BET输出信息 ---")
        print(result.stdout) # 打印标准输出
        print("----------------------")
        return True
        
    except FileNotFoundError:
        print("错误: 'hd-bet' 命令未找到。")
        print("请确保您已经使用 'pip install hd-bet' 安装了它，并且Python环境配置正确。")
        return False
        
    except subprocess.CalledProcessError as e:
        # 如果命令执行失败 (check=True会触发这个)
        print("错误: HD-BET执行失败。")
        print(f"返回码: {e.returncode}")
        print("--- 错误信息 (stderr) ---")
        print(e.stderr) # 打印详细的错误输出，非常重要！
        print("--------------------------")
        return False

# --- 如何使用这个函数 ---
if __name__ == "__main__":
    # !! 重要 !! 请修改为您自己的路径
    # 这是您DICOM转NIfTI后得到的完整头部文件
    full_head_nii = "D:/Path/To/Your/full_head.nii.gz"
    
    # 这是您希望保存脑提取结果的文件路径
    brain_only_nii = "D:/Path/To/Your/brain_only_hd-bet.nii.gz"
    
    # 调用函数执行脑提取
    success = run_brain_extraction_hd_bet(full_head_nii, brain_only_nii)
    
    if success:
        print(f"\n脑提取成功！结果保存在: {brain_only_nii}")
        print("您现在可以将这个文件作为输入，用于后续的配准步骤。")
    else:
        print("\n脑提取失败。请检查上面的错误信息。")