import SimpleITK as sitk
import os
import sys

# --- 步骤 0: 设置文件路径 ---
# !! 重要 !! 请修改为您自己的路径
# 输入：需要被处理的完整头部NIfTI图像
input_head_image_path = "./li_yunqiao_MRI/NIFTI_Output_Folder/Series.nii.gz"

# 输出：最终提取出的大脑图像的保存路径
output_brain_image_path = "./li_yunqiao_MRI/NIFTI_Output_Folder/brain_only_scripted.nii.gz"

# 中间文件保存的文件夹 (用于调试和观察)
# 脚本会自动创建这个文件夹
debug_output_folder = "./li_yunqiao_MRI/mid"


# --- 检查和准备 ---
if not os.path.exists(input_head_image_path):
    print(f"错误: 找不到输入文件: {input_head_image_path}")
    sys.exit(1)
if not os.path.exists(debug_output_folder):
    os.makedirs(debug_output_folder)
    print(f"已创建调试文件夹: {debug_output_folder}")

print("--- 开始脑提取流程 ---")

# --- 加载原始图像 ---
head_image = sitk.ReadImage(input_head_image_path, sitk.sitkFloat32)


# --- 步骤 1: 强度阈值分割 (Otsu方法) ---
# Otsu方法会自动寻找一个最佳阈值，将图像分为前景和背景。
# 在T1图像中，这通常能很好地将头部组织从黑色背景中分离出来。
otsu_filter = sitk.OtsuThresholdImageFilter()
otsu_filter.SetInsideValue(0)  # 背景设为0
otsu_filter.SetOutsideValue(1) # 前景（头部）设为1
binary_mask_step1 = otsu_filter.Execute(head_image)

# 保存这一步的结果以供检查
sitk.WriteImage(binary_mask_step1, os.path.join(debug_output_folder, "1_otsu_threshold_mask.nii.gz"))
print("步骤 1: Otsu阈值分割完成。")


# --- 步骤 2: 连通组件分析 (保留最大组件) ---
# 上一步的掩模中可能包含眼睛、脖子等与大脑分离的组织。
# 我们的目标是只保留最大的那个连通部分，也即大脑。
cc_filter = sitk.ConnectedComponentImageFilter()
labeled_mask = cc_filter.Execute(binary_mask_step1)

relabel_filter = sitk.RelabelComponentImageFilter()
relabel_filter.SortByObjectSize(True) # 按物体大小排序，最大的ID为1
relabeled_mask = relabel_filter.Execute(labeled_mask)

# 创建一个只包含最大组件（标签ID为1）的新掩模
largest_component_mask_step2 = relabeled_mask == 1

# 保存这一步的结果
sitk.WriteImage(largest_component_mask_step2, os.path.join(debug_output_folder, "2_largest_component_mask.nii.gz"))
print("步骤 2: 保留最大连通组件完成。")


# --- 步骤 3: 形态学操作 (精炼掩模) ---
# 此时的掩模可能还不够平滑，或者有一些细小的孔洞。
# 我们使用“闭运算”来填充这些孔洞，使大脑掩模更“实心”。
# 然后使用“开运算”来移除可能粘连在表面的细小非脑组织。

# 闭运算: 先膨胀再腐蚀，用于填充内部的小孔和海湾
closing_filter = sitk.BinaryMorphologicalClosingImageFilter()
closing_filter.SetKernelRadius(2) # 这个半径是关键参数，可能需要调整
closed_mask = closing_filter.Execute(largest_component_mask_step2)

# 开运算: 先腐蚀再膨胀，用于移除外部的细小物体和“毛刺”
opening_filter = sitk.BinaryMorphologicalOpeningImageFilter()
opening_filter.SetKernelRadius(1) # 这个半径也是关键参数
refined_mask_step3 = opening_filter.Execute(closed_mask)

# 保存这一步的结果
sitk.WriteImage(refined_mask_step3, os.path.join(debug_output_folder, "3_morphological_refined_mask.nii.gz"))
print("步骤 3: 形态学操作精炼掩模完成。")


# --- 步骤 4: 填充所有孔洞 (最后一步) ---
# 确保最终的掩模内部是完全实心的。
final_mask_step4 = sitk.BinaryFillholeImageFilter().Execute(refined_mask_step3)

# 保存最终的掩模
sitk.WriteImage(final_mask_step4, os.path.join(debug_output_folder, "4_final_brain_mask.nii.gz"))
print("步骤 4: 填充所有孔洞，生成最终掩模。")


# --- 步骤 5: 应用掩模 ---
# 将最终的掩模应用到原始图像上，所有在掩模之外的像素都将被设为0。
mask_filter = sitk.MaskImageFilter()
final_brain_image = mask_filter.Execute(head_image, final_mask_step4)

# 保存最终结果
sitk.WriteImage(final_brain_image, output_brain_image_path)
print(f"--- 脑提取完成！最终结果已保存至: {output_brain_image_path} ---")