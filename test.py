import SimpleITK as sitk
import os
import sys
import numpy as np

# --- 第1步: 定义文件路径 ---
# !! 重要 !!
# 使用指定的DICOM数据进行配准

# AAL模板文件 (.nii.gz) - 固定图像（目标空间）
fixed_image_path = "./PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"

# DICOM数据目录 - 移动图像（需要配准的图像）
dicom_directory = "./li_yunqiao_MRI/1.2.840.113619.2.353.2807.8104482.22345.1711440338.948"

# 配准完成后，输出文件的保存路径和名称
output_image_path = "./output/dicom_registration/registered_dicom.nii.gz"
output_transform_path = "./output/dicom_registration/dicom_to_aal.tfm"

# --- 检查文件和目录是否存在 ---
if not os.path.exists(fixed_image_path):
    print(f"错误: 找不到固定图像文件: {fixed_image_path}")
    sys.exit(1)
if not os.path.exists(dicom_directory):
    print(f"错误: 找不到DICOM数据目录: {dicom_directory}")
    sys.exit(1)

def load_dicom_series(dicom_dir):
    """
    加载DICOM系列数据
    """
    print(f"正在读取DICOM目录: {dicom_dir}")

    # 获取DICOM系列读取器
    reader = sitk.ImageSeriesReader()

    # 获取DICOM文件名
    dicom_names = reader.GetGDCMSeriesFileNames(dicom_dir)

    if len(dicom_names) == 0:
        print(f"错误: 在目录 {dicom_dir} 中没有找到DICOM文件")
        return None

    print(f"找到 {len(dicom_names)} 个DICOM文件")

    # 设置文件名并读取
    reader.SetFileNames(dicom_names)
    reader.MetaDataDictionaryArrayUpdateOn()
    reader.LoadPrivateTagsOn()

    try:
        image = reader.Execute()
        print(f"DICOM图像加载成功")
        print(f"图像尺寸: {image.GetSize()}")
        print(f"图像间距: {image.GetSpacing()}")
        print(f"图像原点: {image.GetOrigin()}")

        # 获取一些DICOM元数据
        try:
            patient_name = reader.GetMetaData(0, "0010|0010")
            study_date = reader.GetMetaData(0, "0008|0020")
            modality = reader.GetMetaData(0, "0008|0060")
            print(f"患者姓名: {patient_name}")
            print(f"检查日期: {study_date}")
            print(f"影像模态: {modality}")
        except:
            print("无法读取部分DICOM元数据")

        return image

    except Exception as e:
        print(f"读取DICOM数据时出错: {e}")
        return None


# --- 第2步: 加载图像 ---
print("正在加载图像...")

# 加载固定的模板图像 (AAL)
print(f"加载固定图像 (AAL模板): {fixed_image_path}")
fixed_image = sitk.ReadImage(fixed_image_path, sitk.sitkFloat32)
print(f"固定图像尺寸: {fixed_image.GetSize()}")
print(f"固定图像间距: {fixed_image.GetSpacing()}")

# 加载DICOM数据作为移动图像
print(f"加载移动图像 (DICOM数据): {dicom_directory}")
moving_image = load_dicom_series(dicom_directory)

if moving_image is None:
    print("错误: 无法加载DICOM数据")
    sys.exit(1)

# 转换为Float32类型以便配准
moving_image = sitk.Cast(moving_image, sitk.sitkFloat32)

# --- 第2.5步: 图像预处理 ---
print("正在进行图像预处理...")

# 检查图像强度范围并进行归一化
def normalize_image_intensity(image, percentile_lower=1, percentile_upper=99):
    """
    对图像进行强度归一化
    """
    # 转换为numpy数组进行统计
    array = sitk.GetArrayFromImage(image)

    # 计算百分位数
    lower_bound = np.percentile(array[array > 0], percentile_lower)
    upper_bound = np.percentile(array[array > 0], percentile_upper)

    print(f"图像强度范围: {array.min():.2f} - {array.max():.2f}")
    print(f"归一化范围 ({percentile_lower}%-{percentile_upper}%): {lower_bound:.2f} - {upper_bound:.2f}")

    # 使用SimpleITK进行强度窗口化和归一化
    image_windowed = sitk.IntensityWindowing(image,
                                           windowMinimum=lower_bound,
                                           windowMaximum=upper_bound,
                                           outputMinimum=0.0,
                                           outputMaximum=1000.0)

    return image_windowed

# 对两个图像进行归一化
print("归一化固定图像...")
fixed_image = normalize_image_intensity(fixed_image)

print("归一化移动图像...")
moving_image = normalize_image_intensity(moving_image)


# --- 第3步: 初始化变换 ---
print("正在初始化变换...")

# 使用仿射变换 (AffineTransform) 来处理平移、旋转、缩放
initial_transform = sitk.CenteredTransformInitializer(
    fixed_image,
    moving_image,
    sitk.AffineTransform(3),
    sitk.CenteredTransformInitializerFilter.GEOMETRY,
)

print("初始变换参数:")
print(f"  变换类型: {initial_transform.GetName()}")
print(f"  参数数量: {initial_transform.GetNumberOfParameters()}")

# --- 第4步: 设置配准框架 ---
print("正在设置配准方法...")
registration_method = sitk.ImageRegistrationMethod()

# 设置度量 (Metric) - 互信息对于脑部配准很有效
registration_method.SetMetricAsMattesMutualInformation(numberOfHistogramBins=64)
registration_method.SetMetricSamplingStrategy(registration_method.RANDOM)
registration_method.SetMetricSamplingPercentage(0.02)  # 增加采样百分比以提高精度

# 设置插值器 (Interpolator)
registration_method.SetInterpolator(sitk.sitkLinear)

# 设置优化器 (Optimizer) - 针对脑部配准优化参数
registration_method.SetOptimizerAsGradientDescent(
    learningRate=0.5,  # 降低学习率以提高稳定性
    numberOfIterations=300,  # 增加迭代次数
    convergenceMinimumValue=1e-7,  # 更严格的收敛条件
    convergenceWindowSize=15,
)
registration_method.SetOptimizerScalesFromPhysicalShift()

# 设置多分辨率金字塔 (Pyramid) - 针对脑部图像优化
registration_method.SetShrinkFactorsPerLevel(shrinkFactors=[8, 4, 2, 1])  # 增加层级
registration_method.SetSmoothingSigmasPerLevel(smoothingSigmas=[3, 2, 1, 0])  # 对应的平滑参数
registration_method.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()

# 设置初始变换
registration_method.SetInitialTransform(initial_transform)

# 添加观察器来监控配准进度
def command_iteration(method):
    """配准进度回调函数"""
    if method.GetOptimizerIteration() % 20 == 0:
        print(f"  迭代 {method.GetOptimizerIteration():3d}: 度量值 = {method.GetMetricValue():10.5f}")

registration_method.AddCommand(sitk.sitkIterationEvent, lambda: command_iteration(registration_method))

# --- 第5步: 执行配准 ---
print("\n开始执行配准... (这可能需要几分钟时间)")
print("配准进度:")

try:
    final_transform = registration_method.Execute(fixed_image, moving_image)
    print("\n配准完成！")
except Exception as e:
    print(f"\n配准失败: {e}")
    sys.exit(1)

# 打印最终的变换参数和优化器停止条件
print(f"优化器停止条件: {registration_method.GetOptimizerStopConditionDescription()}")
print(f"最终度量值: {registration_method.GetMetricValue():.6f}")
print(f"总迭代次数: {registration_method.GetOptimizerIteration()}")

# --- 第6步: 应用变换并保存结果 ---
print("\n正在应用变换并保存结果...")

# 使用Resample函数将变换应用到移动图像上
# 输出图像的属性(空间、原点等)与固定图像(AAL模板)一致
resampler = sitk.ResampleImageFilter()
resampler.SetReferenceImage(fixed_image)
resampler.SetInterpolator(sitk.sitkLinear)
resampler.SetDefaultPixelValue(0)  # 将图像外的像素设置为0
resampler.SetTransform(final_transform)

resampled_image = resampler.Execute(moving_image)

# 创建输出目录（如果不存在）
output_dir = os.path.dirname(output_image_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 保存配准后的图像和变换文件
sitk.WriteImage(resampled_image, output_image_path)
sitk.WriteTransform(final_transform, output_transform_path)

print(f"✅ 配准后的图像已保存至: {output_image_path}")
print(f"✅ 变换文件已保存至: {output_transform_path}")

# --- 第7步: 保存配准结果 ---
print("\n配准完成，结果已保存")

# --- 第8步: 配准质量评估 ---
print("\n正在评估配准质量...")

def evaluate_registration_quality(fixed_img, moving_img):
    """
    评估配准质量
    """
    try:
        # 计算图像相似性指标
        # 计算均方误差
        diff_img = sitk.Subtract(fixed_img, moving_img)
        squared_diff = sitk.Multiply(diff_img, diff_img)
        mse_value = sitk.GetArrayFromImage(squared_diff).mean()

        # 计算相关系数
        fixed_array = sitk.GetArrayFromImage(fixed_img).flatten()
        moving_array = sitk.GetArrayFromImage(moving_img).flatten()

        # 只计算非零像素的相关性
        mask = (fixed_array > 0) & (moving_array > 0)
        if mask.sum() > 0:
            correlation = np.corrcoef(fixed_array[mask], moving_array[mask])[0, 1]
        else:
            correlation = 0.0

        # 计算重叠度量 (Dice系数)
        fixed_binary = fixed_array > 0
        moving_binary = moving_array > 0
        intersection = (fixed_binary & moving_binary).sum()
        dice_coefficient = 2.0 * intersection / (fixed_binary.sum() + moving_binary.sum())

        return mse_value, correlation, dice_coefficient

    except Exception as e:
        print(f"质量评估时出错: {e}")
        return 0.0, 0.0, 0.0

mse_value, correlation, dice_coefficient = evaluate_registration_quality(fixed_image, resampled_image)

print("配准质量评估:")
print(f"  均方误差 (MSE): {mse_value:.6f}")
print(f"  相关系数 (Correlation): {correlation:.6f}")
print(f"  Dice系数 (重叠度): {dice_coefficient:.6f}")

print("\n" + "="*60)
print("DICOM图像配准完成！")
print("="*60)
print(f"📁 配准后的DICOM图像: {output_image_path}")
print(f"📁 变换文件: {output_transform_path}")
print("="*60)
print("\n💡 配准结果说明:")
print("- DICOM图像已成功配准到AAL模板空间")
print("- 配准后的图像与AAL模板具有相同的空间属性")
print("- 可以使用变换文件将其他图像配准到同一空间")
print("- Dice系数表示配准质量，通常>0.6为良好配准")
print("- 配准后的图像可用于进一步的分析和处理")