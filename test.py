import SimpleITK as sitk
import os
import sys
import numpy as np

# --- 第1步: 定义文件路径 ---
# !! 重要 !!
# 请将这些路径替换为您自己电脑上的实际文件路径

# AAL模板文件 (.nii.gz) - 固定图像（目标空间）
fixed_image_path = "./PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"

# HD-BET提取的脑部图像 (.nii.gz) - 移动图像（需要配准的图像）
moving_image_path = "./li_yunqiao_MRI/NIFTI_Output_Folder/brain_only_hd-bet.nii.gz"

# 配准完成后，输出文件的保存路径和名称
output_image_path = "./output/test_MRI/registered_brain_hd-bet.nii.gz"
output_transform_path = "./output/test_MRI/brain_to_aal.tfm"
output_fusion_path = "./output/test_MRI/fused_brain_aal.nii.gz"

# --- 检查文件是否存在 ---
if not os.path.exists(fixed_image_path):
    print(f"错误: 找不到固定图像文件: {fixed_image_path}")
    sys.exit(1)
if not os.path.exists(moving_image_path):
    print(f"错误: 找不到移动图像文件: {moving_image_path}")
    print("请确保已经运行 brain.py 生成了脑提取图像")
    sys.exit(1)


# --- 第2步: 加载图像 ---
print("正在加载图像...")

# 加载固定的模板图像 (AAL)
print(f"加载固定图像 (AAL模板): {fixed_image_path}")
fixed_image = sitk.ReadImage(fixed_image_path, sitk.sitkFloat32)
print(f"固定图像尺寸: {fixed_image.GetSize()}")
print(f"固定图像间距: {fixed_image.GetSpacing()}")

# 加载HD-BET提取的脑部图像
print(f"加载移动图像 (脑提取结果): {moving_image_path}")
try:
    moving_image = sitk.ReadImage(moving_image_path, sitk.sitkFloat32)
    print(f"移动图像尺寸: {moving_image.GetSize()}")
    print(f"移动图像间距: {moving_image.GetSpacing()}")
except Exception as e:
    print(f"错误: 无法加载脑提取图像。请检查文件路径。")
    print(f"详细错误: {e}")
    sys.exit(1)

# --- 第2.5步: 图像预处理 ---
print("正在进行图像预处理...")

# 检查图像强度范围并进行归一化
def normalize_image_intensity(image, percentile_lower=1, percentile_upper=99):
    """
    对图像进行强度归一化
    """
    # 转换为numpy数组进行统计
    array = sitk.GetArrayFromImage(image)

    # 计算百分位数
    lower_bound = np.percentile(array[array > 0], percentile_lower)
    upper_bound = np.percentile(array[array > 0], percentile_upper)

    print(f"图像强度范围: {array.min():.2f} - {array.max():.2f}")
    print(f"归一化范围 ({percentile_lower}%-{percentile_upper}%): {lower_bound:.2f} - {upper_bound:.2f}")

    # 使用SimpleITK进行强度窗口化和归一化
    image_windowed = sitk.IntensityWindowing(image,
                                           windowMinimum=lower_bound,
                                           windowMaximum=upper_bound,
                                           outputMinimum=0.0,
                                           outputMaximum=1000.0)

    return image_windowed

# 对两个图像进行归一化
print("归一化固定图像...")
fixed_image = normalize_image_intensity(fixed_image)

print("归一化移动图像...")
moving_image = normalize_image_intensity(moving_image)


# --- 第3步: 初始化变换 ---
print("正在初始化变换...")

# 使用仿射变换 (AffineTransform) 来处理平移、旋转、缩放
initial_transform = sitk.CenteredTransformInitializer(
    fixed_image,
    moving_image,
    sitk.AffineTransform(3),
    sitk.CenteredTransformInitializerFilter.GEOMETRY,
)

print("初始变换参数:")
print(f"  变换类型: {initial_transform.GetName()}")
print(f"  参数数量: {initial_transform.GetNumberOfParameters()}")

# --- 第4步: 设置配准框架 ---
print("正在设置配准方法...")
registration_method = sitk.ImageRegistrationMethod()

# 设置度量 (Metric) - 互信息对于脑部配准很有效
registration_method.SetMetricAsMattesMutualInformation(numberOfHistogramBins=64)
registration_method.SetMetricSamplingStrategy(registration_method.RANDOM)
registration_method.SetMetricSamplingPercentage(0.02)  # 增加采样百分比以提高精度

# 设置插值器 (Interpolator)
registration_method.SetInterpolator(sitk.sitkLinear)

# 设置优化器 (Optimizer) - 针对脑部配准优化参数
registration_method.SetOptimizerAsGradientDescent(
    learningRate=0.5,  # 降低学习率以提高稳定性
    numberOfIterations=300,  # 增加迭代次数
    convergenceMinimumValue=1e-7,  # 更严格的收敛条件
    convergenceWindowSize=15,
)
registration_method.SetOptimizerScalesFromPhysicalShift()

# 设置多分辨率金字塔 (Pyramid) - 针对脑部图像优化
registration_method.SetShrinkFactorsPerLevel(shrinkFactors=[8, 4, 2, 1])  # 增加层级
registration_method.SetSmoothingSigmasPerLevel(smoothingSigmas=[3, 2, 1, 0])  # 对应的平滑参数
registration_method.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()

# 设置初始变换
registration_method.SetInitialTransform(initial_transform)

# 添加观察器来监控配准进度
def command_iteration(method):
    """配准进度回调函数"""
    if method.GetOptimizerIteration() % 20 == 0:
        print(f"  迭代 {method.GetOptimizerIteration():3d}: 度量值 = {method.GetMetricValue():10.5f}")

registration_method.AddCommand(sitk.sitkIterationEvent, lambda: command_iteration(registration_method))

# --- 第5步: 执行配准 ---
print("\n开始执行配准... (这可能需要几分钟时间)")
print("配准进度:")

try:
    final_transform = registration_method.Execute(fixed_image, moving_image)
    print("\n配准完成！")
except Exception as e:
    print(f"\n配准失败: {e}")
    sys.exit(1)

# 打印最终的变换参数和优化器停止条件
print(f"优化器停止条件: {registration_method.GetOptimizerStopConditionDescription()}")
print(f"最终度量值: {registration_method.GetMetricValue():.6f}")
print(f"总迭代次数: {registration_method.GetOptimizerIteration()}")

# --- 第6步: 应用变换并保存结果 ---
print("\n正在应用变换并保存结果...")

# 使用Resample函数将变换应用到移动图像上
# 输出图像的属性(空间、原点等)与固定图像(AAL模板)一致
resampler = sitk.ResampleImageFilter()
resampler.SetReferenceImage(fixed_image)
resampler.SetInterpolator(sitk.sitkLinear)
resampler.SetDefaultPixelValue(0)  # 将图像外的像素设置为0
resampler.SetTransform(final_transform)

resampled_image = resampler.Execute(moving_image)

# 创建输出目录（如果不存在）
output_dir = os.path.dirname(output_image_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 保存配准后的图像和变换文件
sitk.WriteImage(resampled_image, output_image_path)
sitk.WriteTransform(final_transform, output_transform_path)

print(f"✅ 配准后的图像已保存至: {output_image_path}")
print(f"✅ 变换文件已保存至: {output_transform_path}")

# --- 第7步: 创建融合图像 ---
print("\n正在创建融合图像...")

def create_fusion_image(fixed_img, moving_img, method="overlay"):
    """
    创建两个图像的融合图像

    参数:
    fixed_img: 固定图像 (AAL模板 - 标签图像)
    moving_img: 移动图像 (配准后的脑部图像)
    method: 融合方法 ("overlay", "colormap", "mask")

    返回:
    融合后的图像
    """
    # 确保两个图像具有相同的尺寸和间距
    if fixed_img.GetSize() != moving_img.GetSize():
        print("警告: 图像尺寸不匹配，正在重新采样...")
        resampler = sitk.ResampleImageFilter()
        resampler.SetReferenceImage(fixed_img)
        resampler.SetInterpolator(sitk.sitkLinear)
        resampler.SetDefaultPixelValue(0)
        resampler.SetTransform(sitk.Transform())
        moving_img = resampler.Execute(moving_img)

    if method == "overlay":
        # 方法1: 叠加显示 - 在脑部图像上叠加AAL边界
        # 创建AAL边界
        aal_edges = sitk.LabelContour(sitk.Cast(fixed_img, sitk.sitkUInt8))

        # 归一化脑部图像
        brain_normalized = sitk.RescaleIntensity(moving_img, 0, 255)

        # 在脑部图像上叠加AAL边界
        fusion_img = sitk.Add(brain_normalized, sitk.Multiply(sitk.Cast(aal_edges, sitk.sitkFloat32), 100))

    elif method == "colormap":
        # 方法2: 彩色映射 - 保留AAL标签但调整强度
        # 将脑部图像作为强度调制器
        brain_normalized = sitk.RescaleIntensity(moving_img, 0.3, 1.0)  # 0.3-1.0范围避免完全黑色

        # 只在有脑组织的地方显示AAL标签
        brain_mask = sitk.BinaryThreshold(moving_img, 1, float('inf'), 1, 0)
        aal_masked = sitk.Mask(fixed_img, brain_mask)

        # 用脑部强度调制AAL标签
        fusion_img = sitk.Multiply(sitk.Cast(aal_masked, sitk.sitkFloat32), brain_normalized)

    elif method == "mask":
        # 方法3: 掩膜方法 - 只在脑组织区域显示AAL标签
        brain_mask = sitk.BinaryThreshold(moving_img, 1, float('inf'), 1, 0)
        fusion_img = sitk.Mask(fixed_img, brain_mask)

    else:
        # 默认方法：简单混合
        fixed_normalized = sitk.RescaleIntensity(fixed_img, 0, 1000)
        moving_normalized = sitk.RescaleIntensity(moving_img, 0, 1000)
        fusion_img = sitk.Add(
            sitk.Multiply(fixed_normalized, 0.4),
            sitk.Multiply(moving_normalized, 0.6)
        )

    return fusion_img

def create_multiple_fusion_images(fixed_img, moving_img):
    """
    创建多种融合图像
    """
    fusion_images = {}

    # 创建不同类型的融合图像
    print("创建叠加融合图像...")
    fusion_images['overlay'] = create_fusion_image(fixed_img, moving_img, "overlay")

    print("创建彩色映射融合图像...")
    fusion_images['colormap'] = create_fusion_image(fixed_img, moving_img, "colormap")

    print("创建掩膜融合图像...")
    fusion_images['mask'] = create_fusion_image(fixed_img, moving_img, "mask")

    return fusion_images

# 创建多种融合图像
fusion_images = create_multiple_fusion_images(fixed_image, resampled_image)

# 保存不同类型的融合图像
fusion_paths = {}
output_base = output_fusion_path.replace('.nii.gz', '')

for method, fusion_img in fusion_images.items():
    fusion_path = f"{output_base}_{method}.nii.gz"
    sitk.WriteImage(fusion_img, fusion_path)
    fusion_paths[method] = fusion_path
    print(f"✅ {method}融合图像已保存至: {fusion_path}")

# 使用掩膜方法作为主要融合结果（保留AAL标签）
main_fusion_image = fusion_images['mask']
sitk.WriteImage(main_fusion_image, output_fusion_path)
print(f"✅ 主要融合图像（掩膜方法）已保存至: {output_fusion_path}")

# --- 第8步: 配准质量评估 ---
print("\n正在评估配准质量...")

def evaluate_registration_quality(fixed_img, moving_img):
    """
    评估配准质量
    """
    try:
        # 计算图像相似性指标
        # 计算均方误差
        diff_img = sitk.Subtract(fixed_img, moving_img)
        squared_diff = sitk.Multiply(diff_img, diff_img)
        mse_value = sitk.GetArrayFromImage(squared_diff).mean()

        # 计算相关系数
        fixed_array = sitk.GetArrayFromImage(fixed_img).flatten()
        moving_array = sitk.GetArrayFromImage(moving_img).flatten()

        # 只计算非零像素的相关性
        mask = (fixed_array > 0) & (moving_array > 0)
        if mask.sum() > 0:
            correlation = np.corrcoef(fixed_array[mask], moving_array[mask])[0, 1]
        else:
            correlation = 0.0

        # 计算重叠度量 (Dice系数)
        fixed_binary = fixed_array > 0
        moving_binary = moving_array > 0
        intersection = (fixed_binary & moving_binary).sum()
        dice_coefficient = 2.0 * intersection / (fixed_binary.sum() + moving_binary.sum())

        return mse_value, correlation, dice_coefficient

    except Exception as e:
        print(f"质量评估时出错: {e}")
        return 0.0, 0.0, 0.0

mse_value, correlation, dice_coefficient = evaluate_registration_quality(fixed_image, resampled_image)

print("配准质量评估:")
print(f"  均方误差 (MSE): {mse_value:.6f}")
print(f"  相关系数 (Correlation): {correlation:.6f}")
print(f"  Dice系数 (重叠度): {dice_coefficient:.6f}")

print("\n" + "="*60)
print("配准和融合完成！")
print("="*60)
print(f"📁 配准后的脑部图像: {output_image_path}")
print(f"📁 变换文件: {output_transform_path}")
print(f"📁 主要融合图像 (保留AAL标签): {output_fusion_path}")
print("\n📁 其他融合图像:")
for method, path in fusion_paths.items():
    method_desc = {
        'overlay': '叠加边界',
        'colormap': '彩色映射',
        'mask': '掩膜方法'
    }
    print(f"   - {method_desc.get(method, method)}: {path}")
print("="*60)
print("\n💡 建议:")
print("- 'mask' 方法: 保留完整的AAL区域标签，适合ROI分析")
print("- 'overlay' 方法: 在脑部图像上显示AAL边界，适合可视化")
print("- 'colormap' 方法: 强度调制的AAL标签，平衡显示效果")