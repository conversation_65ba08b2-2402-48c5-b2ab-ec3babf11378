import SimpleITK as sitk
import os
import sys

import SimpleITK as sitk
import os
import sys

# --- 第1步: 定义文件路径 ---
# !! 重要 !!
# 请将这些路径替换为您自己电脑上的实际文件路径

# AAL模板文件 (.nii.gz)
fixed_image_path = "./PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"

# 包含DICOM切片 (.dcm) 的文件夹
moving_image_dicom_folder = "./li_yunqiao_MRI/1.2.840.113619.2.353.2807.8104482.22345.1711440338.948"

# 配准完成后，输出文件的保存路径和名称
output_image_path = "./output/test_MRI/registered_MRI.nii.gz"
output_transform_path = "./output/test_MRI/mri_to_aal.tfm"

# --- 检查文件和文件夹是否存在 ---
if not os.path.exists(fixed_image_path):
    print(f"错误: 找不到固定图像文件: {fixed_image_path}")
    sys.exit(1)
if not os.path.isdir(moving_image_dicom_folder):
    print(f"错误: 找不到DICOM文件夹: {moving_image_dicom_folder}")
    sys.exit(1)


# --- 第2步: 加载图像 ---
print("正在加载图像...")

# 加载固定的模板图像 (AAL)
fixed_image = sitk.ReadImage(fixed_image_path, sitk.sitkFloat32)

# 从DICOM序列加载移动的MRI图像
# 获取文件夹中所有的DICOM文件名
reader = sitk.ImageSeriesReader()
try:
    dicom_names = reader.GetGDCMSeriesFileNames(moving_image_dicom_folder)
    reader.SetFileNames(dicom_names)
    moving_image = reader.Execute()
    moving_image = sitk.Cast(moving_image, sitk.sitkFloat32)
except Exception as e:
    print(f"错误: 无法从DICOM文件夹加载图像。请检查文件夹路径和DICOM文件。")
    print(f"详细错误: {e}")
    sys.exit(1)


# --- 第3步: 初始化变换 ---
# 使用仿射变换 (AffineTransform) 来处理平移、旋转、缩放
initial_transform = sitk.CenteredTransformInitializer(
    fixed_image,
    moving_image,
    sitk.AffineTransform(3),
    sitk.CenteredTransformInitializerFilter.GEOMETRY,
)

# --- 第4步: 设置配准框架 ---
print("正在设置配准方法...")
registration_method = sitk.ImageRegistrationMethod()

# 设置度量 (Metric) - 互信息对于多模态/不同个体配准很有效
registration_method.SetMetricAsMattesMutualInformation(numberOfHistogramBins=50)
registration_method.SetMetricSamplingStrategy(registration_method.RANDOM)
registration_method.SetMetricSamplingPercentage(0.01)

# 设置插值器 (Interpolator)
registration_method.SetInterpolator(sitk.sitkLinear)

# 设置优化器 (Optimizer)
registration_method.SetOptimizerAsGradientDescent(
    learningRate=1.0,
    numberOfIterations=200, # 迭代次数，可以根据需要增加
    convergenceMinimumValue=1e-6,
    convergenceWindowSize=10,
)
registration_method.SetOptimizerScalesFromPhysicalShift()

# 设置多分辨率金字塔 (Pyramid) 以提高速度和鲁棒性
registration_method.SetShrinkFactorsPerLevel(shrinkFactors=[4, 2, 1])
registration_method.SetSmoothingSigmasPerLevel(smoothingSigmas=[2, 1, 0])
registration_method.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()

# 设置初始变换
registration_method.SetInitialTransform(initial_transform)

# --- 第5步: 执行配准 ---
print("开始执行配准... (这可能需要几分钟时间)")
final_transform = registration_method.Execute(fixed_image, moving_image)
print("配准完成！")

# 打印最终的变换参数和优化器停止条件
print(f"优化器停止条件: {registration_method.GetOptimizerStopConditionDescription()}")
print(f"最终度量值: {registration_method.GetMetricValue()}")

# --- 第6步: 应用变换并保存结果 ---
print("正在应用变换并保存结果...")
# 使用Resample函数将变换应用到移动图像上
# 输出图像的属性(空间、原点等)与固定图像(AAL模板)一致
resampler = sitk.ResampleImageFilter()
resampler.SetReferenceImage(fixed_image)
resampler.SetInterpolator(sitk.sitkLinear)
resampler.SetDefaultPixelValue(0) # 将图像外的像素设置为0
resampler.SetTransform(final_transform)

resampled_image = resampler.Execute(moving_image)

# 创建输出目录（如果不存在）
output_dir = os.path.dirname(output_image_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 保存配准后的图像和变换文件
sitk.WriteImage(resampled_image, output_image_path)
sitk.WriteTransform(final_transform, output_transform_path)

print("-" * 30)
print(f"成功！配准后的图像已保存至: {output_image_path}")
print(f"变换文件已保存至: {output_transform_path}")
print("-" * 30)